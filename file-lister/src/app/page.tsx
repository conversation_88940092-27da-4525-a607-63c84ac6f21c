'use client';

import { useState, useEffect, useCallback } from 'react';
import { Icon, IconName } from '@/components/Icon';
import { ThemeCustomizer } from '@/components/ThemeCustomizer';
import { useTheme } from '@/contexts/ThemeContext';
import { useNavigationHistory } from '@/hooks/useNavigationHistory';
import { useKeyboardNavigation } from '@/hooks/useKeyboardNavigation';

interface FileItem {
  name: string;
  type: string;
  size: number;
  modifyTime: number;
  accessTime: number;
  rights: any;
}

interface ApiResponse {
  success: boolean;
  files?: FileItem[];
  currentPath?: string;
  error?: string;
  details?: string;
}

export default function Home() {
  const [files, setFiles] = useState<FileItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [currentPath, setCurrentPath] = useState<string>('/');
  const [showThemeCustomizer, setShowThemeCustomizer] = useState(false);

  const { colorTheme } = useTheme();

  // Navigation history management
  const {
    canGoBack,
    canGoForward,
    pushPath,
    goBack,
    goForward,
    getCurrentPath,
  } = useNavigationHistory(currentPath);

  // Navigation handlers for keyboard shortcuts
  const handleHistoryBack = useCallback(() => {
    const targetPath = goBack();
    if (targetPath) {
      // Call fetchFiles directly without adding to history
      setLoading(true);
      setError(null);

      fetch(`/api/files?path=${encodeURIComponent(targetPath)}`)
        .then(response => response.json())
        .then((data: ApiResponse) => {
          if (data.success && data.files) {
            setFiles(data.files);
            setCurrentPath(data.currentPath || targetPath);
          } else {
            setError(data.error || 'Failed to fetch files');
          }
        })
        .catch(err => {
          setError('Network error: ' + (err instanceof Error ? err.message : 'Unknown error'));
        })
        .finally(() => {
          setLoading(false);
        });
    }
  }, [goBack]);

  const handleHistoryForward = useCallback(() => {
    const targetPath = goForward();
    if (targetPath) {
      // Call fetchFiles directly without adding to history
      setLoading(true);
      setError(null);

      fetch(`/api/files?path=${encodeURIComponent(targetPath)}`)
        .then(response => response.json())
        .then((data: ApiResponse) => {
          if (data.success && data.files) {
            setFiles(data.files);
            setCurrentPath(data.currentPath || targetPath);
          } else {
            setError(data.error || 'Failed to fetch files');
          }
        })
        .catch(err => {
          setError('Network error: ' + (err instanceof Error ? err.message : 'Unknown error'));
        })
        .finally(() => {
          setLoading(false);
        });
    }
  }, [goForward]);

  // Keyboard navigation
  const { getShortcutText } = useKeyboardNavigation({
    onGoBack: handleHistoryBack,
    onGoForward: handleHistoryForward,
    canGoBack,
    canGoForward,
    disabled: loading || showThemeCustomizer,
  });

  // Memoized handlers for better performance
  const handleOpenThemeCustomizer = useCallback(() => {
    setShowThemeCustomizer(true);
  }, []);

  const handleCloseThemeCustomizer = useCallback(() => {
    setShowThemeCustomizer(false);
  }, []);

  // Initialize files on mount and handle URL-based navigation
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const pathFromUrl = urlParams.get('path') || '/';
    fetchFiles(pathFromUrl, false); // Don't add to history on initial load
  }, []);

  const fetchFiles = useCallback(async (path: string = '/', addToHistory: boolean = true) => {
    setLoading(true);
    setError(null);

    try {
      const url = `/api/files?path=${encodeURIComponent(path)}`;
      const response = await fetch(url);
      const data: ApiResponse = await response.json();

      if (data.success && data.files) {
        setFiles(data.files);
        const actualPath = data.currentPath || path;
        setCurrentPath(actualPath);

        // Add to navigation history only if this is a new navigation
        if (addToHistory) {
          pushPath(actualPath);
        }
      } else {
        setError(data.error || 'Failed to fetch files');
      }
    } catch (err) {
      setError('Network error: ' + (err instanceof Error ? err.message : 'Unknown error'));
    } finally {
      setLoading(false);
    }
  }, [pushPath]);

  const navigateToDirectory = (directoryName: string) => {
    // Build the new path
    const newPath = currentPath === '/'
      ? `/${directoryName}`
      : `${currentPath}/${directoryName}`;
    fetchFiles(newPath, true); // Add to history
  };



  const navigateToRoot = () => {
    fetchFiles('/', true); // Add to history
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleString();
  };

  const getBreadcrumbs = () => {
    if (currentPath === '/') return [{ name: 'Root', path: '/' }];

    const parts = currentPath.split('/').filter(part => part !== '');
    const breadcrumbs = [{ name: 'Root', path: '/' }];

    let currentBreadcrumbPath = '';
    parts.forEach(part => {
      currentBreadcrumbPath += `/${part}`;
      breadcrumbs.push({ name: part, path: currentBreadcrumbPath });
    });

    return breadcrumbs;
  };

  const getFileTypeInfo = (fileName: string, fileType: string) => {
    if (fileType === 'd') {
      return {
        iconName: 'folder' as IconName,
        category: 'Directory',
        colorClass: 'file-type-directory'
      };
    }

    const extension = fileName.split('.').pop()?.toLowerCase() || '';

    // Video files
    if (['mp4', 'avi', 'mkv', 'mov', 'wmv', 'flv', 'webm', 'm4v'].includes(extension)) {
      return {
        iconName: 'file-video' as IconName,
        category: 'Video',
        colorClass: 'file-type-video'
      };
    }

    // Audio files
    if (['mp3', 'wav', 'flac', 'aac', 'ogg', 'm4a', 'wma'].includes(extension)) {
      return {
        iconName: 'file-audio' as IconName,
        category: 'Audio',
        colorClass: 'file-type-audio'
      };
    }

    // Image files
    if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp', 'tiff', 'ico'].includes(extension)) {
      return {
        iconName: 'file-image' as IconName,
        category: 'Image',
        colorClass: 'file-type-image'
      };
    }

    // Document files
    if (['pdf', 'doc', 'docx', 'txt', 'rtf', 'odt'].includes(extension)) {
      return {
        iconName: 'file-text' as IconName,
        category: 'Document',
        colorClass: 'file-type-document'
      };
    }

    // Spreadsheet files
    if (['xls', 'xlsx', 'csv', 'ods'].includes(extension)) {
      return {
        iconName: 'file-spreadsheet' as IconName,
        category: 'Spreadsheet',
        colorClass: 'file-type-document'
      };
    }

    // Presentation files
    if (['ppt', 'pptx', 'odp'].includes(extension)) {
      return {
        iconName: 'file-text' as IconName,
        category: 'Presentation',
        colorClass: 'file-type-document'
      };
    }

    // Archive files
    if (['zip', 'rar', '7z', 'tar', 'gz', 'bz2', 'xz'].includes(extension)) {
      return {
        iconName: 'archive' as IconName,
        category: 'Archive',
        colorClass: 'file-type-archive'
      };
    }

    // Code files
    if (['js', 'ts', 'jsx', 'tsx', 'py', 'java', 'cpp', 'c', 'h', 'css', 'html', 'php', 'rb', 'go', 'rs'].includes(extension)) {
      return {
        iconName: 'file-code' as IconName,
        category: 'Code',
        colorClass: 'file-type-code'
      };
    }

    // Configuration files
    if (['json', 'xml', 'yaml', 'yml', 'toml', 'ini', 'conf', 'config'].includes(extension)) {
      return {
        iconName: 'settings' as IconName,
        category: 'Config',
        colorClass: 'file-type-code'
      };
    }

    // Executable files
    if (['exe', 'msi', 'dmg', 'pkg', 'deb', 'rpm', 'app'].includes(extension)) {
      return {
        iconName: 'file' as IconName,
        category: 'Executable',
        colorClass: 'file-type-video'
      };
    }

    // Default for unknown files
    return {
      iconName: 'file' as IconName,
      category: 'File',
      colorClass: 'file-type-default'
    };
  };

  return (
    <div
      className="min-h-screen p-8"
      style={{ backgroundColor: 'var(--color-background)' }}
    >
      <div className="max-w-6xl mx-auto">
        <div className="flex items-center justify-between mb-8">
          <h1
            className="text-3xl font-bold"
            style={{ color: 'var(--color-foreground)' }}
          >
            Remote File Lister
          </h1>
          <button
            onClick={handleOpenThemeCustomizer}
            className="theme-button-secondary px-4 py-2 rounded-lg font-medium transition-colors flex items-center space-x-2"
          >
            <Icon name="palette" size={20} />
            <span>Customize</span>
          </button>
        </div>

        <div className="mb-6 space-y-4">
          <button
            onClick={() => fetchFiles(currentPath, false)}
            disabled={loading}
            className="theme-button-primary px-6 py-2 rounded-lg font-medium transition-colors flex items-center space-x-2"
          >
            <Icon name="refresh" size={16} />
            <span>{loading ? 'Connecting...' : 'Refresh Files'}</span>
          </button>

          {/* Breadcrumb Navigation */}
          <div className="flex items-center space-x-2 text-sm">
            <span style={{ color: 'var(--color-muted-foreground)' }}>Path:</span>
            {getBreadcrumbs().map((breadcrumb, index) => (
              <div key={breadcrumb.path} className="flex items-center">
                {index > 0 && <span style={{ color: 'var(--color-muted-foreground)' }} className="mx-2">/</span>}
                <button
                  onClick={() => fetchFiles(breadcrumb.path, true)}
                  className="hover:underline transition-colors"
                  style={{ color: 'var(--color-primary)' }}
                  disabled={loading}
                >
                  {breadcrumb.name}
                </button>
              </div>
            ))}
          </div>

          {/* Navigation Buttons */}
          <div className="flex flex-wrap gap-2">
            {/* History Navigation */}
            <div className="flex space-x-2">
              <button
                onClick={handleHistoryBack}
                disabled={loading || !canGoBack}
                className={`theme-button-secondary px-4 py-2 rounded font-medium transition-colors flex items-center space-x-2 ${
                  !canGoBack ? 'opacity-50 cursor-not-allowed' : ''
                }`}
                title={`Go back (${getShortcutText().back})`}
              >
                <Icon name="arrow-left" size={16} />
                <span>Back</span>
                <span className="text-xs opacity-60 ml-1">({getShortcutText().back})</span>
              </button>
              <button
                onClick={handleHistoryForward}
                disabled={loading || !canGoForward}
                className={`theme-button-secondary px-4 py-2 rounded font-medium transition-colors flex items-center space-x-2 ${
                  !canGoForward ? 'opacity-50 cursor-not-allowed' : ''
                }`}
                title={`Go forward (${getShortcutText().forward})`}
              >
                <Icon name="arrow-right" size={16} />
                <span>Forward</span>
                <span className="text-xs opacity-60 ml-1">({getShortcutText().forward})</span>
              </button>
            </div>

            {/* Directory Navigation */}
            {currentPath !== '/' && (
              <button
                onClick={navigateToRoot}
                disabled={loading}
                className="theme-button-secondary px-4 py-2 rounded font-medium transition-colors flex items-center space-x-2"
              >
                <Icon name="home" size={16} />
                <span>Root</span>
              </button>
            )}
          </div>
        </div>

        {error && (
          <div
            className="px-4 py-3 rounded mb-6 border"
            style={{
              backgroundColor: 'var(--color-destructive)',
              color: 'var(--color-destructive-foreground)',
              borderColor: 'var(--color-destructive)'
            }}
          >
            <strong>Error:</strong> {error}
          </div>
        )}

        {files.length > 0 && (
          <div
            className="theme-card rounded-lg shadow overflow-hidden border"
          >
            <div
              className="px-6 py-4 border-b"
              style={{
                backgroundColor: 'var(--color-muted)',
                borderColor: 'var(--color-border)'
              }}
            >
              <h2
                className="text-lg font-semibold"
                style={{ color: 'var(--color-foreground)' }}
              >
                Files in {currentPath} ({files.length} items)
              </h2>
            </div>

            <div className="overflow-x-auto">
              <table className="min-w-full divide-y" style={{ borderColor: 'var(--color-border)' }}>
                <thead style={{ backgroundColor: 'var(--color-muted)' }}>
                  <tr>
                    <th
                      className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider"
                      style={{ color: 'var(--color-muted-foreground)' }}
                    >
                      Name
                    </th>
                    <th
                      className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider"
                      style={{ color: 'var(--color-muted-foreground)' }}
                    >
                      Type
                    </th>
                    <th
                      className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider"
                      style={{ color: 'var(--color-muted-foreground)' }}
                    >
                      Size
                    </th>
                    <th
                      className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider"
                      style={{ color: 'var(--color-muted-foreground)' }}
                    >
                      Modified
                    </th>
                  </tr>
                </thead>
                <tbody
                  className="divide-y"
                  style={{
                    backgroundColor: 'var(--color-card)',
                    borderColor: 'var(--color-border)'
                  }}
                >
                  {files.map((file, index) => {
                    const fileTypeInfo = getFileTypeInfo(file.name, file.type);
                    return (
                      <tr
                        key={index}
                        className="transition-colors"
                        style={{
                          ':hover': { backgroundColor: 'var(--color-muted)' }
                        }}
                        onMouseEnter={(e) => {
                          e.currentTarget.style.backgroundColor = 'var(--color-muted)';
                        }}
                        onMouseLeave={(e) => {
                          e.currentTarget.style.backgroundColor = 'transparent';
                        }}
                      >
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          {file.type === 'd' ? (
                            <button
                              onClick={() => navigateToDirectory(file.name)}
                              className={`flex items-center space-x-3 ${fileTypeInfo.colorClass} hover:underline hover:opacity-80 transition-opacity`}
                              disabled={loading}
                            >
                              <Icon name={fileTypeInfo.iconName} size={20} />
                              <span style={{ color: 'var(--color-foreground)' }}>{file.name}</span>
                            </button>
                          ) : (
                            <div className={`flex items-center space-x-3 ${fileTypeInfo.colorClass}`}>
                              <Icon name={fileTypeInfo.iconName} size={20} />
                              <span style={{ color: 'var(--color-foreground)' }}>{file.name}</span>
                            </div>
                          )}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm">
                          <span
                            className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium`}
                            style={{
                              backgroundColor: file.type === 'd'
                                ? 'var(--color-primary)'
                                : fileTypeInfo.category === 'Video' ? 'var(--color-destructive)'
                                : fileTypeInfo.category === 'Audio' ? 'var(--color-secondary)'
                                : fileTypeInfo.category === 'Image' ? 'var(--color-accent)'
                                : fileTypeInfo.category === 'Document' ? 'var(--color-primary)'
                                : fileTypeInfo.category === 'Code' ? 'var(--color-ring)'
                                : 'var(--color-muted)',
                              color: file.type === 'd'
                                ? 'var(--color-primary-foreground)'
                                : fileTypeInfo.category === 'Video' ? 'var(--color-destructive-foreground)'
                                : fileTypeInfo.category === 'Audio' ? 'var(--color-secondary-foreground)'
                                : fileTypeInfo.category === 'Image' ? 'var(--color-accent-foreground)'
                                : fileTypeInfo.category === 'Document' ? 'var(--color-primary-foreground)'
                                : fileTypeInfo.category === 'Code' ? 'var(--color-background)'
                                : 'var(--color-muted-foreground)'
                            }}
                          >
                            {fileTypeInfo.category}
                          </span>
                        </td>
                        <td
                          className="px-6 py-4 whitespace-nowrap text-sm"
                          style={{ color: 'var(--color-muted-foreground)' }}
                        >
                          {file.type === 'd' ? '-' : formatFileSize(file.size)}
                        </td>
                        <td
                          className="px-6 py-4 whitespace-nowrap text-sm"
                          style={{ color: 'var(--color-muted-foreground)' }}
                        >
                          {formatDate(file.modifyTime)}
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>
          </div>
        )}

        {/* Theme Customizer Modal */}
        <ThemeCustomizer
          isOpen={showThemeCustomizer}
          onClose={handleCloseThemeCustomizer}
        />
      </div>
    </div>
  );
}
