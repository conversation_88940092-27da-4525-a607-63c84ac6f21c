'use client';

import { useEffect, useCallback } from 'react';

interface UseKeyboardNavigationProps {
  onGoBack: () => void;
  onGoForward: () => void;
  canGoBack: boolean;
  canGoForward: boolean;
  disabled?: boolean;
}

export function useKeyboardNavigation({
  onGoBack,
  onGoForward,
  canGoBack,
  canGoForward,
  disabled = false,
}: UseKeyboardNavigationProps) {
  
  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    // Don't handle keyboard events if disabled or if user is typing in an input
    if (disabled || 
        event.target instanceof HTMLInputElement || 
        event.target instanceof HTMLTextAreaElement ||
        event.target instanceof HTMLSelectElement) {
      return;
    }

    // Check for browser back/forward shortcuts
    const isMac = navigator.platform.toUpperCase().indexOf('MAC') >= 0;
    const isWindows = navigator.platform.toUpperCase().indexOf('WIN') >= 0;
    const isLinux = navigator.platform.toUpperCase().indexOf('LINUX') >= 0;

    let shouldGoBack = false;
    let shouldGoForward = false;

    if (isMac) {
      // Mac: Cmd + Left Arrow (back), Cmd + Right Arrow (forward)
      if (event.metaKey && !event.shiftKey && !event.altKey && !event.ctrlKey) {
        if (event.key === 'ArrowLeft') {
          shouldGoBack = true;
        } else if (event.key === 'ArrowRight') {
          shouldGoForward = true;
        }
      }
    } else if (isWindows || isLinux) {
      // Windows/Linux: Alt + Left Arrow (back), Alt + Right Arrow (forward)
      if (event.altKey && !event.shiftKey && !event.metaKey && !event.ctrlKey) {
        if (event.key === 'ArrowLeft') {
          shouldGoBack = true;
        } else if (event.key === 'ArrowRight') {
          shouldGoForward = true;
        }
      }
    }

    // Additional common shortcuts
    // Backspace for back navigation (when not in input fields)
    if (event.key === 'Backspace' && !event.metaKey && !event.altKey && !event.ctrlKey && !event.shiftKey) {
      shouldGoBack = true;
    }

    if (shouldGoBack && canGoBack) {
      event.preventDefault();
      event.stopPropagation();
      onGoBack();
    } else if (shouldGoForward && canGoForward) {
      event.preventDefault();
      event.stopPropagation();
      onGoForward();
    }
  }, [onGoBack, onGoForward, canGoBack, canGoForward, disabled]);

  // Handle browser back/forward button events
  const handlePopState = useCallback((event: PopStateEvent) => {
    if (disabled) return;

    // Extract path from the state or URL
    const path = event.state?.path || new URLSearchParams(window.location.search).get('path') || '/';
    
    // This will be handled by the navigation history hook
    // We just need to prevent the default browser behavior
    console.log('Browser navigation detected, path:', path);
  }, [disabled]);

  useEffect(() => {
    if (disabled) return;

    // Add keyboard event listener
    document.addEventListener('keydown', handleKeyDown, { passive: false });
    
    // Add browser back/forward button listener
    window.addEventListener('popstate', handlePopState);

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      window.removeEventListener('popstate', handlePopState);
    };
  }, [handleKeyDown, handlePopState, disabled]);

  // Return keyboard shortcut information for UI display
  const getShortcutText = useCallback(() => {
    const isMac = navigator.platform.toUpperCase().indexOf('MAC') >= 0;
    
    if (isMac) {
      return {
        back: '⌘←',
        forward: '⌘→',
        backAlt: 'Backspace',
      };
    } else {
      return {
        back: 'Alt+←',
        forward: 'Alt+→',
        backAlt: 'Backspace',
      };
    }
  }, []);

  return {
    getShortcutText,
  };
}
